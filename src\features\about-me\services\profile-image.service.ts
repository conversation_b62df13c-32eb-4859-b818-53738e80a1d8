/**
 * Profile image service for handling photo upload and management
 * Following the co-located feature organization pattern
 */

import type { ProfileImageUploadResult } from '../types/profile.types';

/**
 * Configuration for image upload
 */
const IMAGE_CONFIG = {
  maxFileSize: 5 * 1024 * 1024, // 5MB
  allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  maxWidth: 800,
  maxHeight: 800,
  quality: 0.8,
} as const;

/**
 * Profile image service class
 * Handles image upload, validation, and processing
 */
class ProfileImageService {
  /**
   * Upload and process profile image
   * 
   * @param file - Image file to upload
   * @returns Promise resolving to upload result
   */
  async uploadImage(file: File): Promise<ProfileImageUploadResult> {
    // Validate file
    this.validateFile(file);
    
    try {
      // Process image (resize, compress, etc.)
      const processedImageData = await this.processImage(file);
      
      // In a real implementation, this would upload to a server
      // For now, we'll simulate the upload and return the processed data
      await this.simulateUpload();
      
      return {
        imageData: processedImageData,
        filename: file.name,
        fileSize: file.size,
        uploadedAt: new Date().toISOString(),
      };
    } catch (error) {
      throw new Error(`Failed to upload image: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Remove profile image
   * 
   * @param imageData - Image data to remove
   * @returns Promise that resolves when image is removed
   */
  async removeImage(imageData: string): Promise<void> {
    try {
      // In a real implementation, this would make an API call to delete the image
      await this.simulateRemoval();
      
      // Clean up any local references
      this.cleanupImageData(imageData);
    } catch (error) {
      throw new Error(`Failed to remove image: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Validate uploaded file
   * 
   * @param file - File to validate
   * @throws Error if file is invalid
   */
  private validateFile(file: File): void {
    // Check file size
    if (file.size > IMAGE_CONFIG.maxFileSize) {
      throw new Error(`File size must be less than ${IMAGE_CONFIG.maxFileSize / (1024 * 1024)}MB`);
    }

    // Check file type
    if (!IMAGE_CONFIG.allowedTypes.includes(file.type)) {
      throw new Error(`File type must be one of: ${IMAGE_CONFIG.allowedTypes.join(', ')}`);
    }

    // Check if file is actually an image
    if (!file.type.startsWith('image/')) {
      throw new Error('File must be an image');
    }
  }

  /**
   * Process image (resize, compress, convert to base64)
   * 
   * @param file - Image file to process
   * @returns Promise resolving to processed image data
   */
  private async processImage(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (event) => {
        const img = new Image();
        
        img.onload = () => {
          try {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            if (!ctx) {
              reject(new Error('Failed to get canvas context'));
              return;
            }

            // Calculate new dimensions
            const { width, height } = this.calculateDimensions(
              img.width, 
              img.height, 
              IMAGE_CONFIG.maxWidth, 
              IMAGE_CONFIG.maxHeight
            );

            // Set canvas dimensions
            canvas.width = width;
            canvas.height = height;

            // Draw and compress image
            ctx.drawImage(img, 0, 0, width, height);
            
            // Convert to base64 with compression
            const compressedData = canvas.toDataURL('image/jpeg', IMAGE_CONFIG.quality);
            resolve(compressedData);
          } catch (error) {
            reject(new Error(`Failed to process image: ${error}`));
          }
        };

        img.onerror = () => {
          reject(new Error('Failed to load image'));
        };

        img.src = event.target?.result as string;
      };

      reader.onerror = () => {
        reject(new Error('Failed to read file'));
      };

      reader.readAsDataURL(file);
    });
  }

  /**
   * Calculate optimal dimensions for image resizing
   * 
   * @param originalWidth - Original image width
   * @param originalHeight - Original image height
   * @param maxWidth - Maximum allowed width
   * @param maxHeight - Maximum allowed height
   * @returns Calculated dimensions
   */
  private calculateDimensions(
    originalWidth: number, 
    originalHeight: number, 
    maxWidth: number, 
    maxHeight: number
  ): { width: number; height: number } {
    let { width, height } = { width: originalWidth, height: originalHeight };

    // If image is smaller than max dimensions, keep original size
    if (width <= maxWidth && height <= maxHeight) {
      return { width, height };
    }

    // Calculate aspect ratio
    const aspectRatio = width / height;

    // Resize based on which dimension exceeds the limit more
    if (width / maxWidth > height / maxHeight) {
      width = maxWidth;
      height = width / aspectRatio;
    } else {
      height = maxHeight;
      width = height * aspectRatio;
    }

    return {
      width: Math.round(width),
      height: Math.round(height),
    };
  }

  /**
   * Simulate upload delay for realistic UX
   */
  private async simulateUpload(): Promise<void> {
    const delay = Math.random() * 1000 + 500; // 500-1500ms
    await new Promise(resolve => setTimeout(resolve, delay));
  }

  /**
   * Simulate removal delay for realistic UX
   */
  private async simulateRemoval(): Promise<void> {
    const delay = Math.random() * 500 + 200; // 200-700ms
    await new Promise(resolve => setTimeout(resolve, delay));
  }

  /**
   * Clean up image data references
   * 
   * @param imageData - Image data to clean up
   */
  private cleanupImageData(imageData: string): void {
    // In a real implementation, this might revoke object URLs or clean up cache
    // For base64 data, no cleanup is needed
    if (imageData.startsWith('blob:')) {
      URL.revokeObjectURL(imageData);
    }
  }

  /**
   * Get image metadata without uploading
   * 
   * @param file - Image file to analyze
   * @returns Promise resolving to image metadata
   */
  async getImageMetadata(file: File): Promise<{
    width: number;
    height: number;
    size: number;
    type: string;
  }> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      
      img.onload = () => {
        resolve({
          width: img.width,
          height: img.height,
          size: file.size,
          type: file.type,
        });
      };

      img.onerror = () => {
        reject(new Error('Failed to load image for metadata'));
      };

      img.src = URL.createObjectURL(file);
    });
  }

  /**
   * Check if file is a valid image
   * 
   * @param file - File to check
   * @returns Whether file is a valid image
   */
  isValidImage(file: File): boolean {
    try {
      this.validateFile(file);
      return true;
    } catch {
      return false;
    }
  }
}

/**
 * Singleton instance of the profile image service
 */
export const profileImageService = new ProfileImageService();

/**
 * Export individual methods for easier testing and mocking
 */
export const {
  uploadImage,
  removeImage,
  getImageMetadata,
  isValidImage,
} = profileImageService;
