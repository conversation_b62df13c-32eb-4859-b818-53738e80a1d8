import { register } from '@ceridianhcm/platform-state';
import { profileApi } from '../api/profile.api';
import { profileImageService } from '../services/profile-image.service';
import type { 
  ProfileData, 
  ProfileEditFormData, 
  ProfileImageUploadResult,
  PronounOption 
} from '../types/profile.types';

/**
 * ProfileStore manages profile data and editing state using Platform State.
 * 
 * Features:
 * - Profile data management
 * - Photo upload and removal
 * - Form state synchronization
 * - Loading and error states
 * - Optimistic updates
 * 
 * @example
 * ```tsx
 * const { profileData, isLoading, updateProfile } = useSource(ProfileStore);
 * ```
 */
class ProfileStore {
  // Profile data state
  profileData: ProfileData | null = null;
  
  // Form editing state
  isEditing = false;
  editFormData: ProfileEditFormData | null = null;
  
  // Loading states
  isLoading = false;
  isSubmitting = false;
  isUploadingPhoto = false;
  
  // Error states
  error: string | null = null;
  uploadError: string | null = null;
  
  // Photo management
  photoData: string | null = null;
  originalPhotoData: string | null = null;
  
  // Dropdown options
  pronounOptions: PronounOption[] = [];

  /**
   * Initialize the store with profile data
   */
  async initialize(employeeId: number): Promise<void> {
    this.setLoading(true);
    this.clearError();
    
    try {
      const [profileResponse, pronounsResponse] = await Promise.all([
        profileApi.getProfile(employeeId),
        profileApi.getPronouns()
      ]);
      
      this.profileData = profileResponse.data;
      this.pronounOptions = pronounsResponse.data;
      this.originalPhotoData = this.profileData?.profilePhoto || null;
      this.photoData = this.originalPhotoData;
    } catch (error) {
      this.setError(error instanceof Error ? error.message : 'Failed to load profile');
    } finally {
      this.setLoading(false);
    }
  }

  /**
   * Start editing mode
   */
  startEditing(): void {
    if (!this.profileData) return;
    
    this.isEditing = true;
    this.editFormData = {
      profilePhoto: this.profileData.profilePhoto || '',
      biography: this.profileData.biography || '',
      pronouns: this.profileData.pronouns || '',
    };
    this.photoData = this.originalPhotoData;
    this.clearError();
  }

  /**
   * Cancel editing and revert changes
   */
  cancelEditing(): void {
    this.isEditing = false;
    this.editFormData = null;
    this.photoData = this.originalPhotoData;
    this.clearError();
    this.clearUploadError();
  }

  /**
   * Update form field value
   */
  updateFormField<K extends keyof ProfileEditFormData>(
    field: K, 
    value: ProfileEditFormData[K]
  ): void {
    if (!this.editFormData) return;
    
    this.editFormData = {
      ...this.editFormData,
      [field]: value,
    };
  }

  /**
   * Handle photo upload
   */
  async uploadPhoto(file: File): Promise<string> {
    this.isUploadingPhoto = true;
    this.clearUploadError();
    
    try {
      const result: ProfileImageUploadResult = await profileImageService.uploadImage(file);
      this.photoData = result.imageData;
      
      // Update form data if editing
      if (this.editFormData) {
        this.editFormData.profilePhoto = result.imageData;
      }
      
      return result.imageData;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to upload photo';
      this.setUploadError(errorMessage);
      throw error;
    } finally {
      this.isUploadingPhoto = false;
    }
  }

  /**
   * Remove photo
   */
  async removePhoto(): Promise<void> {
    this.isUploadingPhoto = true;
    this.clearUploadError();
    
    try {
      if (this.photoData) {
        await profileImageService.removeImage(this.photoData);
      }
      
      this.photoData = null;
      
      // Update form data if editing
      if (this.editFormData) {
        this.editFormData.profilePhoto = '';
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to remove photo';
      this.setUploadError(errorMessage);
      throw error;
    } finally {
      this.isUploadingPhoto = false;
    }
  }

  /**
   * Submit profile changes
   */
  async submitProfile(data: ProfileEditFormData): Promise<void> {
    if (!this.profileData) throw new Error('No profile data available');
    
    this.isSubmitting = true;
    this.clearError();
    
    try {
      const submitData = {
        ...data,
        profilePhoto: this.photoData || data.profilePhoto,
      };
      
      const response = await profileApi.updateProfile(this.profileData.employeeId, submitData);
      
      // Update local state with response
      this.profileData = response.data;
      this.originalPhotoData = this.photoData;
      this.isEditing = false;
      this.editFormData = null;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update profile';
      this.setError(errorMessage);
      throw error;
    } finally {
      this.isSubmitting = false;
    }
  }

  /**
   * Reset store to initial state
   */
  reset(): void {
    this.profileData = null;
    this.isEditing = false;
    this.editFormData = null;
    this.isLoading = false;
    this.isSubmitting = false;
    this.isUploadingPhoto = false;
    this.error = null;
    this.uploadError = null;
    this.photoData = null;
    this.originalPhotoData = null;
    this.pronounOptions = [];
  }

  // Private helper methods
  private setLoading(loading: boolean): void {
    this.isLoading = loading;
  }

  private setError(error: string): void {
    this.error = error;
  }

  private clearError(): void {
    this.error = null;
  }

  private setUploadError(error: string): void {
    this.uploadError = error;
  }

  private clearUploadError(): void {
    this.uploadError = null;
  }

  // Computed properties
  get isFormValid(): boolean {
    return !!this.editFormData && (
      !!this.editFormData.biography?.trim() ||
      !!this.editFormData.pronouns ||
      !!this.editFormData.profilePhoto
    );
  }

  get hasChanges(): boolean {
    if (!this.profileData || !this.editFormData) return false;
    
    return (
      this.editFormData.biography !== (this.profileData.biography || '') ||
      this.editFormData.pronouns !== (this.profileData.pronouns || '') ||
      this.photoData !== this.originalPhotoData
    );
  }

  get isProcessing(): boolean {
    return this.isLoading || this.isSubmitting || this.isUploadingPhoto;
  }
}

export const ProfileStore = register(ProfileStore, 'ProfileStore');
