---
type: "manual"
---

TITLE: Testing React Components with React Testing Library
DESCRIPTION: A complete test example showing how to use React Testing Library to test the HiddenMessage component. It demonstrates querying elements, triggering events, and making assertions with jest-dom.
SOURCE: https://github.com/testing-library/react-testing-library/blob/main/README.md#2025-04-12_snippet_2

LANGUAGE: jsx
CODE:
```
// __tests__/hidden-message.js
// these imports are something you'd normally configure Jest to import for you
// automatically. Learn more in the setup docs: https://testing-library.com/docs/react-testing-library/setup#cleanup
import '@testing-library/jest-dom'
// NOTE: jest-dom adds handy assertions to Jest and is recommended, but not required

import * as React from 'react'
import {render, fireEvent, screen} from '@testing-library/react'
import HiddenMessage from '../hidden-message'

test('shows the children when the checkbox is checked', () => {
  const testMessage = 'Test Message'
  render(<HiddenMessage>{testMessage}</HiddenMessage>)

  // query* functions will return the element or null if it cannot be found
  // get* functions will return the element or throw an error if it cannot be found
  expect(screen.queryByText(testMessage)).toBeNull()

  // the queries can accept a regex to make your selectors more resilient to content tweaks and changes.
  fireEvent.click(screen.getByLabelText(/show/i))

  // .toBeInTheDocument() is an assertion that comes from jest-dom
  // otherwise you could use .toBeDefined()
  expect(screen.getByText(testMessage)).toBeInTheDocument()
})
```

----------------------------------------

TITLE: Testing React Login Component with Mock Service Worker
DESCRIPTION: This test suite demonstrates how to test a React login component with RTL and Mock Service Worker. It includes tests for successful login and error handling, showing how to mock API responses, simulate user interactions, and verify UI updates and side effects.
SOURCE: https://github.com/testing-library/react-testing-library/blob/main/README.md#2025-04-12_snippet_4

LANGUAGE: jsx
CODE:
```
// __tests__/login.js
// again, these first two imports are something you'd normally handle in
// your testing framework configuration rather than importing them in every file.
import '@testing-library/jest-dom'
import * as React from 'react'
// import API mocking utilities from Mock Service Worker.
import {rest} from 'msw'
import {setupServer} from 'msw/node'
// import testing utilities
import {render, fireEvent, screen} from '@testing-library/react'
import Login from '../login'

const fakeUserResponse = {token: 'fake_user_token'}
const server = setupServer(
  rest.post('/api/login', (req, res, ctx) => {
    return res(ctx.json(fakeUserResponse))
  }),
)

beforeAll(() => server.listen())
afterEach(() => {
  server.resetHandlers()
  window.localStorage.removeItem('token')
})
afterAll(() => server.close())

test('allows the user to login successfully', async () => {
  render(<Login />)

  // fill out the form
  fireEvent.change(screen.getByLabelText(/username/i), {
    target: {value: 'chuck'},
  })
  fireEvent.change(screen.getByLabelText(/password/i), {
    target: {value: 'norris'},
  })

  fireEvent.click(screen.getByText(/submit/i))

  // just like a manual tester, we'll instruct our test to wait for the alert
  // to show up before continuing with our assertions.
  const alert = await screen.findByRole('alert')

  // .toHaveTextContent() comes from jest-dom's assertions
  // otherwise you could use expect(alert.textContent).toMatch(/congrats/i)
  // but jest-dom will give you better error messages which is why it's recommended
  expect(alert).toHaveTextContent(/congrats/i)
  expect(window.localStorage.getItem('token')).toEqual(fakeUserResponse.token)
})

test('handles server exceptions', async () => {
  // mock the server error response for this test suite only.
  server.use(
    rest.post('/api/login', (req, res, ctx) => {
      return res(ctx.status(500), ctx.json({message: 'Internal server error'}))
    }),
  )

  render(<Login />)

  // fill out the form
  fireEvent.change(screen.getByLabelText(/username/i), {
    target: {value: 'chuck'},
  })
  fireEvent.change(screen.getByLabelText(/password/i), {
    target: {value: 'norris'},
  })

  fireEvent.click(screen.getByText(/submit/i))

  // wait for the error message
  const alert = await screen.findByRole('alert')

  expect(alert).toHaveTextContent(/internal server error/i)
  expect(window.localStorage.getItem('token')).toBeNull()
})
```

----------------------------------------

TITLE: Implementing a React Login Component with API Integration
DESCRIPTION: This code creates a React login component that handles form submission, API calls, and state management. It includes form validation, loading states, and success/error messages displayed to the user.
SOURCE: https://github.com/testing-library/react-testing-library/blob/main/README.md#2025-04-12_snippet_3

LANGUAGE: jsx
CODE:
```
// login.js
import * as React from 'react'

function Login() {
  const [state, setState] = React.useReducer((s, a) => ({...s, ...a}), {
    resolved: false,
    loading: false,
    error: null,
  })

  function handleSubmit(event) {
    event.preventDefault()
    const {usernameInput, passwordInput} = event.target.elements

    setState({loading: true, resolved: false, error: null})

    window
      .fetch('/api/login', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({
          username: usernameInput.value,
          password: passwordInput.value,
        }),
      })
      .then(r => r.json().then(data => (r.ok ? data : Promise.reject(data))))
      .then(
        user => {
          setState({loading: false, resolved: true, error: null})
          window.localStorage.setItem('token', user.token)
        },
        error => {
          setState({loading: false, resolved: false, error: error.message})
        },
      )
  }

  return (
    <div>
      <form onSubmit={handleSubmit}>
        <div>
          <label htmlFor="usernameInput">Username</label>
          <input id="usernameInput" />
        </div>
        <div>
          <label htmlFor="passwordInput">Password</label>
          <input id="passwordInput" type="password" />
        </div>
        <button type="submit">Submit{state.loading ? '...' : null}</button>
      </form>
      {state.error ? <div role="alert">{state.error}</div> : null}
      {state.resolved ? (
        <div role="alert">Congrats! You're signed in!</div>
      ) : null}
    </div>
  )
}

export default Login
```

----------------------------------------

TITLE: React Component Implementation with Hooks
DESCRIPTION: A simple React functional component called HiddenMessage that uses the useState hook to control the visibility of its children. This component serves as the subject under test in the example.
SOURCE: https://github.com/testing-library/react-testing-library/blob/main/README.md#2025-04-12_snippet_1

LANGUAGE: jsx
CODE:
```
// hidden-message.js
import * as React from 'react'

// NOTE: React Testing Library works well with React Hooks and classes.
// Your tests will be the same regardless of how you write your components.
function HiddenMessage({children}) {
  const [showMessage, setShowMessage] = React.useState(false)
  return (
    <div>
      <label htmlFor="toggle">Show Message</label>
      <input
        id="toggle"
        type="checkbox"
        onChange={e => setShowMessage(e.target.checked)}
        checked={showMessage}
      />
      {showMessage ? children : null}
    </div>
  )
}

export default HiddenMessage
```

----------------------------------------

TITLE: Suppressing act() Warnings in React DOM 16.8
DESCRIPTION: A workaround to silence unnecessary act() warnings when using React Testing Library with React DOM 16.8. This snippet overwrites console.error to ignore specific warning patterns related to the act() function.
SOURCE: https://github.com/testing-library/react-testing-library/blob/main/README.md#2025-04-12_snippet_0

LANGUAGE: javascript
CODE:
```
// this is just a little hack to silence a warning that we'll get until we
// upgrade to 16.9. See also: https://github.com/facebook/react/pull/14853
const originalError = console.error
beforeAll(() => {
  console.error = (...args) => {
    if (/Warning.*not wrapped in act/.test(args[0])) {
      return
    }
    originalError.call(console, ...args)
  }
})

afterAll(() => {
  console.error = originalError
})
```

----------------------------------------

TITLE: Configuring Git Remote for React Testing Library
DESCRIPTION: Commands to set up Git remote configuration for contributing to React Testing Library. This ensures your main branch stays synced with the upstream repository.
SOURCE: https://github.com/testing-library/react-testing-library/blob/main/CONTRIBUTING.md#2025-04-12_snippet_0

LANGUAGE: bash
CODE:
```
git remote add upstream https://github.com/testing-library/react-testing-library.git
git fetch upstream
git branch --set-upstream-to=upstream/main main
```

----------------------------------------

TITLE: Commit Message Template for Manual Major Release
DESCRIPTION: Template for the commit message required to manually trigger a major version release. It includes the fix type, release purpose, reference to relevant issue, and required BREAKING CHANGE section.
SOURCE: https://github.com/testing-library/react-testing-library/blob/main/other/manual-releases.md#2025-04-12_snippet_0

LANGUAGE: markdown
CODE:
```
fix(release): manually release a major version

There was an issue with a major release, so this manual-releases.md
change is to release a new major version.

Reference: #<the number of a relevant pull request, issue, or commit>

BREAKING CHANGE: <mention any relevant breaking changes (this is what triggers the major version change so don't skip this!)>
```

----------------------------------------

TITLE: Commit Message Template for Manual Minor Release
DESCRIPTION: Template for the commit message required to manually trigger a minor version release. It includes the feat type, release purpose, and reference to relevant issue.
SOURCE: https://github.com/testing-library/react-testing-library/blob/main/other/manual-releases.md#2025-04-12_snippet_1

LANGUAGE: markdown
CODE:
```
feat(release): manually release a minor version

There was an issue with a minor release, so this manual-releases.md
change is to release a new minor version.

Reference: #<the number of a relevant pull request, issue, or commit>
```

----------------------------------------

TITLE: Commit Message Template for Manual Patch Release
DESCRIPTION: Template for the commit message required to manually trigger a patch version release. It includes the fix type, release purpose, and reference to relevant issue.
SOURCE: https://github.com/testing-library/react-testing-library/blob/main/other/manual-releases.md#2025-04-12_snippet_2

LANGUAGE: markdown
CODE:
```
fix(release): manually release a patch version

There was an issue with a patch release, so this manual-releases.md
change is to release a new patch version.

Reference: #<the number of a relevant pull request, issue, or commit>
```

----------------------------------------

TITLE: Referencing Commit Message Convention Link
DESCRIPTION: Link to the conventional changelog angular commit message format that drives the semantic versioning releases. This is critical for maintainers to understand as it directly affects version numbering.
SOURCE: https://github.com/testing-library/react-testing-library/blob/main/other/MAINTAINING.md#2025-04-12_snippet_1

LANGUAGE: markdown
CODE:
```
https://github.com/conventional-changelog-archived-repos/conventional-changelog-angular/blob/ed32559941719a130bb0327f886d6a32a8cbc2ba/convention.md
```

----------------------------------------

TITLE: Referencing Validation Workflow in GitHub Actions
DESCRIPTION: Shows the path to the GitHub workflow file that runs validation actions automatically when pull requests are received. This helps maintainers understand where to find the CI configuration.
SOURCE: https://github.com/testing-library/react-testing-library/blob/main/other/MAINTAINING.md#2025-04-12_snippet_0

LANGUAGE: markdown
CODE:
```
`.github/workflows/validate.yml`
```

----------------------------------------

TITLE: Markdown Template for User Listings
DESCRIPTION: HTML comment containing the template format for adding new users to the list. Shows how to format entries with company/project name links and application links.
SOURCE: https://github.com/testing-library/react-testing-library/blob/main/other/USERS.md#2025-04-12_snippet_0

LANGUAGE: markdown
CODE:
```
<!--
This file should just be a bulleted list like this:

- [Company/Project/Person](https://example.com) uses it in [some app](https://example.com)
-->
```

----------------------------------------

TITLE: HTML Contributors Table Markup
DESCRIPTION: HTML table structure used to display contributor information including avatars, names, and contribution types. The table is formatted with alignment attributes and percentage-based widths for consistent layout.
SOURCE: https://github.com/testing-library/react-testing-library/blob/main/README.md#2025-04-12_snippet_5

LANGUAGE: html
CODE:
```
<table>
  <tbody>
    <tr>
      <td align="center" valign="top" width="14.28%"><a href="https://kentcdodds.com"><img src="https://avatars.githubusercontent.com/u/1500684?v=3?s=100" width="100px;" alt="Kent C. Dodds"/><br /><sub><b>Kent C. Dodds</b></sub></a><br /><a href="https://github.com/testing-library/react-testing-library/commits?author=kentcdodds" title="Code">💻</a> <a href="https://github.com/testing-library/react-testing-library/commits?author=kentcdodds" title="Documentation">📖</a> <a href="#infra-kentcdodds" title="Infrastructure (Hosting, Build-Tools, etc)">🚇</a> <a href="https://github.com/testing-library/react-testing-library/commits?author=kentcdodds" title="Tests">⚠️</a></td>
      <!-- Additional contributor entries removed for brevity -->
    </tr>
  </tbody>
</table>
```

----------------------------------------

TITLE: HTML Contributors Table Structure
DESCRIPTION: HTML table markup showing contributor information including avatars, names, and contribution types indicated by emoji icons. Each contributor cell has a consistent layout with profile image, name and contribution badges.
SOURCE: https://github.com/testing-library/react-testing-library/blob/main/README.md#2025-04-12_snippet_6

LANGUAGE: HTML
CODE:
```
<td align="center" valign="top" width="14.28%"><a href="https://github.com/johann-sonntagbauer"><img src="https://avatars3.githubusercontent.com/u/1239401?v=4?s=100" width="100px;" alt="Johann Hubert Sonntagbauer"/><br /><sub><b>Johann Hubert Sonntagbauer</b></sub></a><br /><a href="https://github.com/testing-library/react-testing-library/commits?author=johann-sonntagbauer" title="Code">💻</a> <a href="https://github.com/testing-library/react-testing-library/commits?author=johann-sonntagbauer" title="Documentation">📖</a> <a href="https://github.com/testing-library/react-testing-library/commits?author=johann-sonntagbauer" title="Tests">⚠️</a></td>
```

----------------------------------------

TITLE: Rendering HTML Contributors Table in React Testing Library
DESCRIPTION: An HTML table structure that displays contributor information including profile pictures, names, and contribution badges. Each contributor cell includes GitHub profile links, avatar images, and contribution type indicators.
SOURCE: https://github.com/testing-library/react-testing-library/blob/main/README.md#2025-04-12_snippet_7

LANGUAGE: HTML
CODE:
```
<td align="center" valign="top" width="14.28%"><a href="https://github.com/timbonicus"><img src="https://avatars0.githubusercontent.com/u/556258?v=4?s=100" width="100px;" alt="Tim"/><br /><sub><b>Tim</b></sub></a><br /><a href="https://github.com/testing-library/react-testing-library/issues?q=author%3Atimbonicus" title="Bug reports">🐛</a> <a href="https://github.com/testing-library/react-testing-library/commits?author=timbonicus" title="Code">💻</a> <a href="https://github.com/testing-library/react-testing-library/commits?author=timbonicus" title="Documentation">📖</a> <a href="https://github.com/testing-library/react-testing-library/commits?author=timbonicus" title="Tests">⚠️</a></td>
```

----------------------------------------

TITLE: Rendering Contributors Table in HTML
DESCRIPTION: HTML table structure displaying contributor information including profile pictures, names, and contribution types using aligned table cells with consistent width and vertical alignment.
SOURCE: https://github.com/testing-library/react-testing-library/blob/main/README.md#2025-04-12_snippet_8

LANGUAGE: HTML
CODE:
```
<td align="center" valign="top" width="14.28%"><a href="https://twitter.com/sebsilbermann"><img src="https://avatars3.githubusercontent.com/u/12292047?v=4?s=100" width="100px;" alt="Sebastian Silbermann"/><br /><sub><b>Sebastian Silbermann</b></sub></a><br /><a href="https://github.com/testing-library/react-testing-library/pulls?q=is%3Apr+reviewed-by%3Aeps1lon" title="Reviewed Pull Requests">👀</a></td>
```

----------------------------------------

TITLE: Contributors Table HTML Structure
DESCRIPTION: HTML table structure displaying contributor information including profile images, names, and contribution types. Uses GitHub avatar URLs and includes contribution indicators.
SOURCE: https://github.com/testing-library/react-testing-library/blob/main/README.md#2025-04-12_snippet_9

LANGUAGE: HTML
CODE:
```
<td align="center" valign="top" width="14.28%"><a href="http://tkdodo.eu"><img src="https://avatars.githubusercontent.com/u/1021430?v=4?s=100" width="100px;" alt="Dominik Dorfmeister"/><br /><sub><b>Dominik Dorfmeister</b></sub></a><br /><a href="https://github.com/testing-library/react-testing-library/commits?author=TkDodo" title="Code">💻</a></td>
```

----------------------------------------

TITLE: Markdown Reference Links
DESCRIPTION: Markdown-style reference link definitions for project badges, social links, and documentation references used throughout the README.
SOURCE: https://github.com/testing-library/react-testing-library/blob/main/README.md#2025-04-12_snippet_10

LANGUAGE: Markdown
CODE:
```
[npm]: https://www.npmjs.com/
[yarn]: https://classic.yarnpkg.com
[node]: https://nodejs.org
[build-badge]: https://img.shields.io/github/actions/workflow/status/testing-library/react-testing-library/validate.yml?branch=main&logo=github
```