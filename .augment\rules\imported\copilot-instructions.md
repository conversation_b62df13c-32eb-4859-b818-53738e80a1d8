---
type: "manual"
---

# HR Profile UI Microfrontend Coding Standards
### Top things we dont document
  - apologies for mistakes you make when coding just fix them and move on
  - dont ask for permission to do something, just do it
## Project Overview
This is a HR Profile UI microfrontend project. When generating code, consider that this is a React-based microfrontend that may be used both standalone and within the Dayforce application shell.

## TypeScript Guidelines
- Use TypeScript for all new code
- Follow functional programming principles where possible
- Use interfaces for data structures and type definitions
- Prefer immutable data (const, readonly)
- Use optional chaining (?.) and nullish coalescing (??) operators
- Include proper TypeScript type definitions for all functions and components

## React Guidelines
- Use functional components with hooks
- Follow React hooks rules (no conditional hooks)
- Keep components small and focused
- Use CSS modules for component styling
- Implement proper error boundaries for feature components
- Use Platform State for state management

## Folder Structure Guidelines
- Group related files in descriptive folders
- Limit nesting to 2-3 levels deep
- Organize feature folders with consistent internal structure:

  ```
  components/
  |
  features/
  ├── feature-name/
  │   ├── components/
  │   ├── hooks/
  │   ├── store/
  │   └── views/
  ```

## Naming Conventions

### Folders
- Use **kebab-case** for general purpose folders: `user-management/`, `payment-processing/`
- Use **lowercase** for standard folders: `components/`, `hooks/`, `utils/`
- Use singular for concept folders: `store/`, `context/`, `hook/`
- Use plural for collection folders: `components/`, `utils/`, `types/`
- Use the pattern `<feature-name>` for feature component folders

### Features
- Use the pattern `<feature-name>-<component-name>` for feature component files
- Feature component files should follow the format:
  - Component: `<feature-name><component-name>.tsx` **PascalCase**
  - Test: `<feature-name>-<component-name>.spec.tsx`  **kebab-case**
  - Styles: `<feature-name>-<component-name>.scss`  **kebab-case**
- Examples:
  - `AboutMeBio.tsx`
  - `about-me-bio.spec.tsx`
  - `about-me-bio.scss`
- Keep feature names consistent across all related files
- Use kebab-case for both feature name and component name parts

### Files
- Use **PascalCase** for React components: `Button.tsx`, `UserProfile.tsx`
- Use **camelCase** for utilities: `formatDate.ts`, `validateInput.ts`
- Add **lowercase** `.spec` suffix for test files: `button.spec.tsx` **lowercase**
- Use **lowercase** `.scss` for CSS import : `button.scss` 'import './button.scss'

### Components and Functions
- Use **PascalCase** for React components and classes
- Use **camelCase** for functions, methods, and variables
- Prefix higher-order components with `with`
- Prefix boolean-returning functions with `is`, `has`, or `should`
- Prefix custom hooks with `use`
- Always update corressponding test when updating components

### CSS/SCSS
- Use **kebab-case** for class selectors in SCSS files
- Use **camelCase** for class names in CSS modules when used in components
- Use **kebab-case** for SCSS variables and mixins

## Store (Platform State) Conventions
- Use **kebab-case** with `store` suffix for store classes
- Use **camelCase** for store methods
- Prefix private/internal methods with underscore (_)
- Export stores using the Platform State register function

## Code Style
- Add proper JSDoc comments for public APIs
- Keep functions small and focused
- Use early returns for cleaner code
- Follow DRY (Don't Repeat Yourself) principles
- Use meaningful variable and function names
- Implement proper error handling with try/catch for async operations

## Testing Guidelines
- Write unit tests for all new components and utilities
- Use Jest and React Testing Library
- Test component rendering and interactions
- Mock external dependencies and API calls
- Follow AAA pattern (Arrange, Act, Assert)
- Write each test one by one and make sure you dont break or change current functionality
- When asked to show coverage please use this command [ pnpm test --coverage --coverageReporters="text" --coverageReporters="text-summary"]

- Use **kebab-case** for test files
EXAMPLE component: `UserProfile.tsx`
EXAMPLE test: `user-profile.spec.tsx`

## Module Federation
- Use **camelCase** for microfrontend names in module federation config
- Use descriptive names for exposed modules
- Consider sharing context when using multiple entry points

## Import/Export Guidelines
- Group imports in order: external libraries, internal modules, component-level imports
- Use barrel files (index.ts) for feature exports
- Prefer named exports for utility functions
- Use default exports for main components

## Error Handling
- Implement proper error boundaries in React components
- Use try/catch blocks for async operations
- Always log errors with contextual information
- Handle edge cases and provide fallback UI when needed

When generating code, please follow these conventions strictly and ensure all new code integrates well with the existing codebase structure.