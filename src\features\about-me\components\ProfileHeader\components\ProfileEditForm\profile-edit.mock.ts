import { ProfileEditFormData } from './profile-edit-form-schema';
import { ProfileEditApiResponse, ProfileImageUploadResponse } from './profile-edit.api';

/**
 * Mock profile data for development and testing
 */
export const mockProfileData: ProfileEditFormData = {
  profilePhoto: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
  pronouns: 'they/them',
  biography: 'Passionate software engineer with 8+ years of experience building scalable web applications. I love working with modern technologies and mentoring junior developers.',
};

/**
 * Mock API service for profile editing operations
 * Used for development and testing when real API is not available
 */
export class MockProfileEditApiService {
  private delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

  /**
   * Mock profile update
   */
  async updateProfile(data: ProfileEditFormData): Promise<ProfileEditApiResponse> {
    await this.delay(1000); // Simulate network delay

    // Simulate occasional failures for testing error handling
    if (Math.random() < 0.1) {
      return {
        success: false,
        error: 'Network error: Failed to update profile',
      };
    }

    return {
      success: true,
      data: {
        ...mockProfileData,
        ...data,
      },
    };
  }

  /**
   * Mock image upload
   */
  async uploadProfileImage(file: File): Promise<ProfileImageUploadResponse> {
    await this.delay(2000); // Simulate longer upload time

    // Simulate file size validation
    if (file.size > 5 * 1024 * 1024) { // 5MB limit
      return {
        success: false,
        error: 'File size too large. Maximum size is 5MB.',
      };
    }

    // Simulate file type validation
    if (!file.type.startsWith('image/')) {
      return {
        success: false,
        error: 'Invalid file type. Please upload an image file.',
      };
    }

    // Simulate occasional upload failures
    if (Math.random() < 0.15) {
      return {
        success: false,
        error: 'Upload failed: Server error',
      };
    }

    // Create a mock base64 image data URL
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onload = () => {
        resolve({
          success: true,
          imageData: reader.result as string,
        });
      };
      reader.readAsDataURL(file);
    });
  }

  /**
   * Mock image removal
   */
  async removeProfileImage(): Promise<ProfileEditApiResponse> {
    await this.delay(500);

    // Simulate occasional failures
    if (Math.random() < 0.05) {
      return {
        success: false,
        error: 'Failed to remove image',
      };
    }

    return {
      success: true,
    };
  }

  /**
   * Mock get current profile
   */
  async getProfile(): Promise<ProfileEditApiResponse> {
    await this.delay(800);

    return {
      success: true,
      data: mockProfileData,
    };
  }
}

/**
 * Singleton instance of the mock profile edit API service
 */
export const mockProfileEditApiService = new MockProfileEditApiService();

/**
 * Utility function to determine which API service to use
 * In development, use mock service; in production, use real API
 */
export const getProfileEditApiService = () => {
  // Use environment variable or build flag to determine which service to use
  const useMockApi = process.env.NODE_ENV === 'development' || process.env.REACT_APP_USE_MOCK_API === 'true';
  
  if (useMockApi) {
    return mockProfileEditApiService;
  }
  
  // In a real implementation, return the actual API service
  // return profileEditApiService;
  return mockProfileEditApiService; // For now, always use mock
};
