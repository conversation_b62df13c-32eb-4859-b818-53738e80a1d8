import { ProfileEditFormData } from './profile-edit-form-schema';

/**
 * Profile edit API response interface
 */
export interface ProfileEditApiResponse {
  success: boolean;
  data?: ProfileEditFormData;
  error?: string;
}

/**
 * Profile image upload response interface
 */
export interface ProfileImageUploadResponse {
  success: boolean;
  imageData?: string;
  error?: string;
}

/**
 * API service for profile editing operations
 */
export class ProfileEditApiService {
  private baseUrl = '/api/profile';

  /**
   * Submit profile updates
   */
  async updateProfile(data: ProfileEditFormData): Promise<ProfileEditApiResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/update`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return {
        success: true,
        data: result,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update profile',
      };
    }
  }

  /**
   * Upload profile image
   */
  async uploadProfileImage(file: File): Promise<ProfileImageUploadResponse> {
    try {
      const formData = new FormData();
      formData.append('image', file);

      const response = await fetch(`${this.baseUrl}/upload-image`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return {
        success: true,
        imageData: result.imageData,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to upload image',
      };
    }
  }

  /**
   * Remove profile image
   */
  async removeProfileImage(): Promise<ProfileEditApiResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/remove-image`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return {
        success: true,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to remove image',
      };
    }
  }

  /**
   * Get current profile data
   */
  async getProfile(): Promise<ProfileEditApiResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/current`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return {
        success: true,
        data: result,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch profile',
      };
    }
  }
}

/**
 * Singleton instance of the profile edit API service
 */
export const profileEditApiService = new ProfileEditApiService();
