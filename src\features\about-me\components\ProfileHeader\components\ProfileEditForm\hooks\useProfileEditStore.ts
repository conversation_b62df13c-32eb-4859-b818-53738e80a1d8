import { useSource } from '@ceridianhcm/platform-state';
import { UseFormReturn } from 'react-hook-form';
import { ProfileEditFormData } from '../profile-edit-form-schema';
import { profileEditStore, ProfileEditStoreState, ProfileEditStoreActions } from '../store/profile-edit.store';

/**
 * Hook for managing profile edit logic with sophisticated state management.
 * This hook is designed to work with react-hook-form and provides centralized state management
 * for profile editing functionality.
 *
 * Features:
 * - Photo upload and removal handling
 * - Form state management
 * - Loading and error states
 * - Profile submission
 *
 * @param methods - Optional UseFormReturn instance from react-hook-form
 * @returns {Object} state and actions for profile editing
 *
 * @example
 * ```tsx
 * const methods = useForm<ProfileEditFormData>();
 * const { state, actions } = useProfileEditForm(methods);
 *
 * // Access state
 * const { isLoading, error, photoData } = state;
 *
 * // Use actions
 * await actions.handlePhotoUpload(file);
 * await actions.submitProfile(data);
 * ```
 */
export const useProfileEditStore = (
  methods?: UseFormReturn<ProfileEditFormData>,
): {
  state: ProfileEditStoreState;
  actions: ProfileEditStoreActions;
} => {
  const store = useSource(profileEditStore);

  // Set form methods when provided
  if (methods) {
    store.setMethods(methods);
  }

  return {
    state: {
      isLoading: store.isLoading,
      error: store.error,
      photoData: store.photoData,
      pronouns: store.pronouns,
      biography: store.biography,
      pronounOptions: store.getPronounOptions(),
      isValid: store.isValid(),
    },
    actions: {
      handlePhotoUpload: store.handlePhotoUpload.bind(store),
      handlePhotoRemove: store.handlePhotoRemove.bind(store),
      submitProfile: store.submitProfile.bind(store),
      setPronounChoice: store.setPronounChoice.bind(store),
      setBiography: store.setBiography.bind(store),
      reset: store.reset.bind(store),
    },
  };
};
