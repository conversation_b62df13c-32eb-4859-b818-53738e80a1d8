---
type: "manual"
---

# Platform State Instructions
  import { register, useSource, subscribe } from '@ceridianhcm/platform-state';

## Overview

Platform State is a state management solution designed for React applications. It provides a simple, class-based approach to managing state with minimal boilerplate.

## Core Concepts

### Source Class
- ES6 class containing properties and actions
- No constructor needed - use `initialize` method instead
- Properties hold atomic values
- Actions can be mutators, getters, or both
- Name in `register` must be unique across the App Shell

### Key Methods
- `initialize`: Optional method for setup when the source is instantiated
- `useSource`: React hook to access source properties and actions
- `register`: Registers a source class globally
- `subscribe`: Non-React way to subscribe to state changes
## Usage
### Basic Setup
1. Create a source class with properties and actions. 


#### `register(source: Class, name?: string)`
Registers a source class globally:
```typescript
class CountStore {
  count = 1;
  increment() {
    this.count++;
  }
}

export const CountStore = register(CountStore, 'CountStore');
```

#### `useSource(source: Class | string)`
Hook to access store properties and actions:
```typescript
function Counter() {
  const { count, increment } = useSource(CountStore);
  return (
    <>
      <div>{count}</div>
      <button onClick={increment}>Increment</button>
    </>
  );
}
```

#### `subscribe(source: string | Class, callback: (state) => void)`
Non-React way to subscribe to state changes:
```typescript
subscribe('CountStore', (newState) => {
  document.getElementById('count').innerHTML = newState.count;
}).then((state) => {
  document.getElementById('button')
    .addEventListener('click', state.increment);
});
```

## Advanced Features

### Initialize Method
Used for setup when source is instantiated:
```typescript
class CountStore {
  count = 1;
  initialize() {
    this.count = Math.floor(Math.random() * 360);
  }
  increment() {
    this.count++;
  }
}
```

### Async Initialize
For async initialization with loading state:
```typescript
class FormStore {
  firstName = '';
  lastName = '';
  async initialize() {
    const data = await fetch('api/user');
    const { firstName, lastName } = await data.json();
    this.firstName = firstName;
    this.lastName = lastName;
  }
}
```
Access loading state with `$loading`:
```typescript
const { firstName, lastName, $loading } = useSource(FormStore);
```

### Selectors
Optimize rerenders by selecting specific properties:
```typescript
// Create a selector
const FirstNameSource = select(FormStore, 'firstName');

// Use in component
function FirstName() {
  const { firstName } = useSource(FirstNameSource);
  return <span>{firstName}</span>;
}
```

### Non-mutable Actions
Prefix with underscore to prevent rerenders:
```typescript
class FormStore {
  firstName = '';
  lastName = '';
  
  _updateSilently(data: any) {
    this.firstName = data.firstName;
    this.lastName = data.lastName;
  }
}
```

## Best Practices

1. Keep sources minimal and focused
2. Use selectors for performance optimization
3. Export registered sources for type safety
4. Use async initialize for data fetching
5. Consider using non-mutable actions for batched updates
6. Prefer object destructuring when using useSource
7. Don't use getters (get syntax) in source classes

## Examples

### Basic Counter
```typescript
// store/counter.ts
export const CounterStore = register(class CounterStore {
  count = 0;
  
  increment() {
    this.count++;
  }
  
  decrement() {
    this.count--;
  }
});

// components/Counter.tsx
function Counter() {
  const { count, increment, decrement } = useSource(CounterStore);
  
  return (
    <div>
      <button onClick={decrement}>-</button>
      <span>{count}</span>
      <button onClick={increment}>+</button>
    </div>
  );
}
```

### Form with Async Data
```typescript
// store/form.ts
export const FormStore = register(class FormStore {
  firstName = '';
  lastName = '';
  
  async initialize() {
    const data = await fetch('/api/user');
    const user = await data.json();
    this.firstName = user.firstName;
    this.lastName = user.lastName;
  }
  
  setFirstName(value: string) {
    this.firstName = value;
  }
  
  setLastName(value: string) {
    this.lastName = value;
  }
});

// components/Form.tsx
function Form() {
  const { firstName, lastName, setFirstName, setLastName, $loading } = useSource(FormStore);
  
  if ($loading) return <div>Loading...</div>;
  
  return (
    <form>
      <input 
        value={firstName}
        onChange={e => setFirstName(e.target.value)}
      />
      <input 
        value={lastName}
        onChange={e => setLastName(e.target.value)}
      />
    </form>
  );
}
```

## Common Patterns

### External Store Access
```typescript
// Using another microfrontend's store
import { ExternalStore } from '@ceridianhcm/other-ui';

function MyComponent() {
  const { data } = useSource(ExternalStore);
  return <div>{data}</div>;
}
```

### Batch Updates
```typescript
class BatchStore {
  value1 = '';
  value2 = '';
  
  _batchUpdate(data: any) {
    this.value1 = data.value1;
    this.value2 = data.value2;
  }
  
  triggerUpdate(data: any) {
    this._batchUpdate(data);
    // Only one render will occur
  }
}
```
