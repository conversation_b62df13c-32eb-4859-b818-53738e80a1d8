import { register } from '@ceridianhcm/platform-state';
import { UseFormReturn } from 'react-hook-form';
import { ProfileEditFormData } from '../profile-edit-form-schema';
import { getProfileEditApiService } from '../profile-edit.mock';

/**
 * State interface for ProfileEditStore
 */
export interface ProfileEditStoreState {
  isLoading: boolean;
  error: string | null;
  photoData: string | null;
  pronouns: string;
  biography: string;
  pronounOptions: Array<{ id: string; title: string }>;
  isValid: boolean;
}

/**
 * Actions interface for ProfileEditStore
 */
export interface ProfileEditStoreActions {
  handlePhotoUpload: (file: File) => Promise<string>;
  handlePhotoRemove: () => Promise<void>;
  submitProfile: (data: ProfileEditFormData) => Promise<void>;
  setPronounChoice: (pronouns: string) => void;
  setBiography: (biography: string) => void;
  reset: () => void;
}

class ProfileEditStore {
  isLoading = false;
  error: string | null = null;
  photoData: string | null = null;
  pronouns = '';
  biography = '';
  private formMethods: UseFormReturn<ProfileEditFormData> | null = null;
  private apiService = getProfileEditApiService();

  /**
   * Initialize the store with default values and optionally load profile data
   */
  async initialize(loadProfile = false) {
    this.isLoading = false;
    this.error = null;
    this.photoData = null;
    this.pronouns = '';
    this.biography = '';
    this.formMethods = null;

    if (loadProfile) {
      await this.loadProfile();
    }
  }

  /**
   * Load current profile data from API
   */
  async loadProfile() {
    this.startLoading();
    try {
      const response = await this.apiService.getProfile();
      if (response.success && response.data) {
        this.pronouns = response.data.pronouns || '';
        this.biography = response.data.biography || '';
        this.photoData = response.data.profilePhoto || null;

        // Update form if available
        if (this.formMethods) {
          this.formMethods.setValue('pronouns', this.pronouns);
          this.formMethods.setValue('biography', this.biography);
          this.formMethods.setValue('profilePhoto', this.photoData || '');
        }
      } else {
        this.setError(response.error || 'Failed to load profile');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load profile';
      this.setError(errorMessage);
    } finally {
      this.stopLoading();
    }
  }

  /**
   * Set React Hook Form methods for integration
   */
  setMethods(methods: UseFormReturn<ProfileEditFormData>) {
    this.formMethods = methods;
  }

  /**
   * Get available pronoun options
   */
  getPronounOptions(): Array<{ id: string; title: string }> {
    return [
      { id: 'he/him', title: 'He/Him' },
      { id: 'she/her', title: 'She/Her' },
      { id: 'they/them', title: 'They/Them' },
      { id: 'other', title: 'Other' },
      { id: 'prefer-not-to-say', title: 'Prefer not to say' },
    ];
  }

  /**
   * Set pronoun choice
   */
  setPronounChoice(pronouns: string) {
    this.pronouns = pronouns;
    this.formMethods?.setValue('pronouns', pronouns);
  }

  /**
   * Set biography text
   */
  setBiography(biography: string) {
    this.biography = biography;
    this.formMethods?.setValue('biography', biography);
  }

  /**
   * Check if form is valid
   */
  isValid(): boolean {
    return this.formMethods?.formState.isValid ?? false;
  }

  /**
   * Reset store to initial state
   */
  reset() {
    this.initialize();
    this.formMethods?.reset();
  }

  private startLoading() {
    this.isLoading = true;
    this.error = null;
  }

  private stopLoading() {
    this.isLoading = false;
  }

  private setError(error: string) {
    this.error = error;
  }

  async handlePhotoUpload(file: File): Promise<string> {
    this.startLoading();
    try {
      const response = await this.apiService.uploadProfileImage(file);
      if (response.success && response.imageData) {
        this.photoData = response.imageData;
        this.formMethods?.setValue('profilePhoto', response.imageData);
        return response.imageData;
      } else {
        const errorMessage = response.error || 'Failed to upload photo';
        this.setError(errorMessage);
        throw new Error(errorMessage);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to upload photo';
      this.setError(errorMessage);
      throw err;
    } finally {
      this.stopLoading();
    }
  }

  async handlePhotoRemove(): Promise<void> {
    this.startLoading();
    try {
      const response = await this.apiService.removeProfileImage();
      if (response.success) {
        this.photoData = null;
        this.formMethods?.setValue('profilePhoto', '');
      } else {
        const errorMessage = response.error || 'Failed to remove photo';
        this.setError(errorMessage);
        throw new Error(errorMessage);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to remove photo';
      this.setError(errorMessage);
      throw err;
    } finally {
      this.stopLoading();
    }
  }

  async submitProfile(data: ProfileEditFormData): Promise<void> {
    this.startLoading();
    try {
      const submitData = {
        ...data,
        profilePhoto: this.photoData || data.profilePhoto,
        pronouns: this.pronouns || data.pronouns,
        biography: this.biography || data.biography,
      };

      const response = await this.apiService.updateProfile(submitData);

      if (response.success) {
        // Update local state with submitted data
        this.pronouns = submitData.pronouns || '';
        this.biography = submitData.biography || '';

        console.log('Profile updated successfully:', submitData);
      } else {
        const errorMessage = response.error || 'Failed to update profile';
        this.setError(errorMessage);
        throw new Error(errorMessage);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update profile';
      this.setError(errorMessage);
      throw err;
    } finally {
      this.stopLoading();
    }
  }
}

export const profileEditStore = register(ProfileEditStore, 'profileEditStore');
