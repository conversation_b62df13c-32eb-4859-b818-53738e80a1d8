import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import { ProfileEditForm } from './ProfileEditForm';
import { useProfileEditStore } from './hooks/useProfileEditStore';
import { ProfileEditFormData } from './profile-edit-form-schema';

// Mock the platform state hook
jest.mock('./hooks/useProfileEditStore', () => ({
  useProfileEditStore: jest.fn(),
}));

// Mock zod schema validation
jest.mock('./profile-edit-form-schema', () => ({
  profileEditFormSchema: {
    parseAsync: jest.fn().mockImplementation(async (data) => data),
  },
  ProfileEditFormData: {},
}));

describe('ProfileEditForm', () => {
  const defaultProps = {
    testId: 'test-profile-edit-form',
  };

  const mockStoreReturn = {
    state: {
      isLoading: false,
      error: null,
      photoData: 'test-photo-data',
      pronouns: '',
      biography: '',
      pronounOptions: [
        { id: 'he/him', title: 'He/Him' },
        { id: 'she/her', title: 'She/Her' },
        { id: 'they/them', title: 'They/Them' },
      ],
      isValid: true,
    },
    actions: {
      handlePhotoUpload: jest.fn().mockResolvedValue('new-photo-data'),
      handlePhotoRemove: jest.fn(),
      submitProfile: jest.fn(),
      setPronounChoice: jest.fn(),
      setBiography: jest.fn(),
      reset: jest.fn(),
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useProfileEditStore as jest.Mock).mockReturnValue(mockStoreReturn);
  });

  it('throws error when not wrapped in ProfileEditProvider', () => {
    (useProfileEditContext as jest.Mock).mockReturnValue(undefined);
    expect(() => render(<ProfileEditForm {...defaultProps} />)).toThrow(
      'Required form methods or actions not available. Ensure ProfileEditForm is wrapped in ProfileEditProvider',
    );
  });

  it('renders photo section', () => {
    render(
      <ProfileEditProvider initialData={{ profilePhoto: '', biography: '', pronouns: '' }}>
        <ProfileEditForm {...defaultProps} />
      </ProfileEditProvider>,
    );

    expect(screen.getByTestId('profile-photo-photo')).toBeInTheDocument();
    expect(screen.getByTestId('upload-photo-button')).toBeInTheDocument();
    expect(screen.getByTestId('remove-photo-button')).toBeInTheDocument();
  });

  it('handles photo upload', async () => {
    render(
      <ProfileEditProvider initialData={{ profilePhoto: '', biography: '', pronouns: '' }}>
        <ProfileEditForm {...defaultProps} />
      </ProfileEditProvider>,
    );

    const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const input = document.querySelector('input[type="file"]');
    if (input) {
      await waitFor(() => {
        fireEvent.change(input, { target: { files: [file] } });
      });
    }

    expect(mockContext.actions.handlePhotoUpload).toHaveBeenCalledWith(file);
    expect(mockContext.methods.setValue).toHaveBeenCalledWith('profilePhoto', 'new-photo-data');
  });

  it('handles photo remove', async () => {
    mockContext.methods.watch.mockReturnValue('test.jpg');

    render(
      <ProfileEditProvider initialData={{ profilePhoto: 'test.jpg', biography: '', pronouns: '' }}>
        <ProfileEditForm {...defaultProps} />
      </ProfileEditProvider>,
    );

    const removeButton = screen.getByTestId('remove-photo-button');
    await waitFor(() => {
      fireEvent.click(removeButton);
    });

    expect(mockContext.actions.handlePhotoRemove).toHaveBeenCalled();
    expect(mockContext.methods.setValue).toHaveBeenCalledWith('profilePhoto', '');
  });

  it('submits form using context', async () => {
    const testData: ProfileEditFormData = {
      profilePhoto: 'test.jpg',
      pronouns: 'they/them',
      biography: 'test bio',
    };

    mockContext.methods.watch.mockImplementation((field) => testData[field as keyof ProfileEditFormData]);

    mockContext.methods.handleSubmit.mockImplementation((cb) => {
      return (e?: React.FormEvent) => {
        e?.preventDefault();
        mockContext.actions.submitProfile(testData);
        return cb();
      };
    });

    render(
      <ProfileEditProvider initialData={testData}>
        <ProfileEditForm {...defaultProps} />
      </ProfileEditProvider>,
    );

    const form = screen.getByTestId('test-profile-edit-form');
    await waitFor(() => {
      fireEvent.submit(form);
    });

    expect(mockContext.actions.submitProfile).toHaveBeenCalledWith(testData);
  });

  it('validates and submits form data correctly', async () => {
    const mockSubmit = jest.fn();
    const mockFormData = {
      profilePhoto: 'test.jpg',
      biography: 'Test bio',
      pronouns: 'they/them',
    };

    const handleSubmit = jest.fn((cb) => async (e: React.FormEvent) => {
      e.preventDefault();
      await cb(mockFormData);
    });

    const mockMethods = {
      watch: jest.fn((field) => mockFormData[field as keyof typeof mockFormData] || ''),
      setValue: jest.fn(),
      trigger: jest.fn().mockResolvedValue(true),
      handleSubmit,
      formState: {
        errors: {},
        isSubmitting: false,
        isDirty: true,
        isValid: true,
      },
    };

    (useProfileEditContext as jest.Mock).mockReturnValue({
      state: {
        isLoading: false,
        error: null,
        photoData: 'test-photo-data',
      },
      methods: mockMethods,
      actions: {
        handlePhotoUpload: jest.fn().mockResolvedValue('new-photo-data'),
        handlePhotoRemove: jest.fn(),
        submitProfile: mockSubmit,
      },
    });

    render(<ProfileEditForm {...defaultProps} />);

    // Find and submit the form
    const form = screen.getByTestId('test-profile-edit-form');
    await act(async () => {
      fireEvent.submit(form);
    });

    // Wait for the submission to complete
    await waitFor(() => {
      expect(mockSubmit).toHaveBeenCalledWith(mockFormData);
    });
  });

  it('handles photo upload correctly', async () => {
    const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const mockUpload = jest.fn().mockResolvedValue('new-photo-url');

    (useProfileEditContext as jest.Mock).mockReturnValue({
      ...mockContext,
      actions: {
        ...mockContext.actions,
        handlePhotoUpload: mockUpload,
      },
    });

    render(<ProfileEditForm {...defaultProps} />);

    const fileInput = screen.getByTestId('photo-upload-input');

    await act(async () => {
      fireEvent.change(fileInput, { target: { files: [mockFile] } });
    });

    await waitFor(() => {
      expect(mockUpload).toHaveBeenCalledWith(mockFile);
    });
  });
});
