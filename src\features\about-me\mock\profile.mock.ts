/**
 * Mock data for profile components following the established pattern
 * Used for development, testing, and storybook examples
 */

import type {
  ProfileData,
  ProfileEditFormData,
  PronounOption,
  ProfileImageUploadResult,
} from '../types/profile.types';

/**
 * Mock profile data for <PERSON>
 */
export const mockProfileData: ProfileData = {
  employeeId: 12345,
  fullName: '<PERSON>',
  jobTitle: 'Senior Software Engineer',
  profilePhoto: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxjaXJjbGUgY3g9IjUwIiBjeT0iMzciIHI9IjE4IiBmaWxsPSIjOUNBM0FGIi8+CjxwYXRoIGQ9Ik0yMCA4M0MyMCA3MS45NTQzIDI4Ljk1NDMgNjMgNDAgNjNINjBDNzEuMDQ1NyA2MyA4MCA3MS45NTQzIDgwIDgzVjEwMEgyMFY4M1oiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+',
  biography: 'Passionate software engineer with 8+ years of experience in full-stack development. Specializes in React, TypeScript, and cloud architecture. Enjoys mentoring junior developers and contributing to open-source projects.',
  pronouns: 'he/him',
  location: 'Toronto, ON, Canada',
  manager: {
    name: '<PERSON>',
    title: 'Engineering Manager',
    employeeId: 67890,
  },
  lengthOfService: {
    years: 3,
    months: 7,
    startDate: '2021-06-15',
  },
  lastUpdated: '2024-12-19T10:30:00Z',
};

/**
 * Mock profile data for Jane Smith (Manager)
 */
export const mockManagerProfileData: ProfileData = {
  employeeId: 67890,
  fullName: 'Jane Smith',
  jobTitle: 'Engineering Manager',
  profilePhoto: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRUZGNkZGIi8+CjxjaXJjbGUgY3g9IjUwIiBjeT0iMzciIHI9IjE4IiBmaWxsPSIjQjc4NUQ5Ii8+CjxwYXRoIGQ9Ik0yMCA4M0MyMCA3MS45NTQzIDI4Ljk1NDMgNjMgNDAgNjNINjBDNzEuMDQ1NyA2MyA4MCA3MS45NTQzIDgwIDgzVjEwMEgyMFY4M1oiIGZpbGw9IiNCNzg1RDkiLz4KPC9zdmc+',
  biography: 'Experienced engineering leader with a passion for building high-performing teams. Focuses on technical excellence, career development, and creating inclusive work environments.',
  pronouns: 'she/her',
  location: 'Toronto, ON, Canada',
  manager: {
    name: 'Robert Johnson',
    title: 'Director of Engineering',
    employeeId: 11111,
  },
  lengthOfService: {
    years: 5,
    months: 2,
    startDate: '2019-10-01',
  },
  lastUpdated: '2024-12-18T15:45:00Z',
};

/**
 * Mock profile data for testing edge cases
 */
export const mockMinimalProfileData: ProfileData = {
  employeeId: 99999,
  fullName: 'Alex Johnson',
  jobTitle: 'Junior Developer',
  lastUpdated: '2024-12-19T08:00:00Z',
};

/**
 * Mock form data for editing
 */
export const mockEditFormData: ProfileEditFormData = {
  profilePhoto: mockProfileData.profilePhoto,
  biography: mockProfileData.biography,
  pronouns: mockProfileData.pronouns,
};

/**
 * Mock pronoun options for dropdown
 */
export const mockPronounOptions: PronounOption[] = [
  { value: '', label: 'Select pronouns', isCommon: false },
  { value: 'he/him', label: 'he/him', isCommon: true },
  { value: 'she/her', label: 'she/her', isCommon: true },
  { value: 'they/them', label: 'they/them', isCommon: true },
  { value: 'xe/xem', label: 'xe/xem', isCommon: false },
  { value: 'ze/zir', label: 'ze/zir', isCommon: false },
  { value: 'prefer-not-to-say', label: 'Prefer not to say', isCommon: false },
];

/**
 * Mock image upload result
 */
export const mockImageUploadResult: ProfileImageUploadResult = {
  imageData: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=',
  filename: 'profile-photo.jpg',
  fileSize: 2048,
  uploadedAt: '2024-12-19T10:30:00Z',
};

/**
 * Mock error responses for testing
 */
export const mockApiErrors = {
  networkError: new Error('Network request failed'),
  validationError: new Error('Invalid profile data provided'),
  unauthorizedError: new Error('Unauthorized access'),
  notFoundError: new Error('Profile not found'),
  serverError: new Error('Internal server error'),
};

/**
 * Mock loading states for testing
 */
export const mockLoadingStates = {
  initial: {
    isLoading: false,
    isSubmitting: false,
    isUploadingPhoto: false,
  },
  loading: {
    isLoading: true,
    isSubmitting: false,
    isUploadingPhoto: false,
  },
  submitting: {
    isLoading: false,
    isSubmitting: true,
    isUploadingPhoto: false,
  },
  uploading: {
    isLoading: false,
    isSubmitting: false,
    isUploadingPhoto: true,
  },
  allLoading: {
    isLoading: true,
    isSubmitting: true,
    isUploadingPhoto: true,
  },
};

/**
 * Mock form validation scenarios
 */
export const mockFormValidation = {
  valid: {
    profilePhoto: mockProfileData.profilePhoto,
    biography: 'Valid biography text',
    pronouns: 'they/them',
  },
  invalidBiography: {
    profilePhoto: mockProfileData.profilePhoto,
    biography: 'x'.repeat(1001), // Too long
    pronouns: 'they/them',
  },
  invalidPronouns: {
    profilePhoto: mockProfileData.profilePhoto,
    biography: 'Valid biography text',
    pronouns: 'invalid/pronouns',
  },
  empty: {
    profilePhoto: '',
    biography: '',
    pronouns: '',
  },
};

/**
 * Mock file objects for testing photo upload
 */
export const mockFiles = {
  validImage: new File(['mock image data'], 'profile.jpg', { type: 'image/jpeg' }),
  invalidType: new File(['mock data'], 'document.pdf', { type: 'application/pdf' }),
  tooLarge: new File(['x'.repeat(10 * 1024 * 1024)], 'large.jpg', { type: 'image/jpeg' }),
};

/**
 * Helper function to create mock profile data with custom values
 */
export const createMockProfile = (overrides: Partial<ProfileData> = {}): ProfileData => ({
  ...mockProfileData,
  ...overrides,
});

/**
 * Helper function to create mock form data with custom values
 */
export const createMockFormData = (overrides: Partial<ProfileEditFormData> = {}): ProfileEditFormData => ({
  ...mockEditFormData,
  ...overrides,
});

/**
 * Helper function to generate random employee ID for testing
 */
export const generateMockEmployeeId = (): number => 
  Math.floor(Math.random() * 90000) + 10000;

/**
 * Helper function to create mock API response
 */
export const createMockApiResponse = <T>(data: T, success = true, message?: string) => ({
  data,
  success,
  message,
  timestamp: new Date().toISOString(),
});
