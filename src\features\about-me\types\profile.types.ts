/**
 * Type definitions for profile-related data structures
 * Following the co-located feature organization pattern
 */

/**
 * Core profile data structure
 */
export interface ProfileData {
  /** Employee unique identifier */
  employeeId: number;
  /** Employee full name */
  fullName: string;
  /** Employee job title */
  jobTitle: string;
  /** Profile photo URL or base64 data */
  profilePhoto?: string;
  /** Employee biography/description */
  biography?: string;
  /** Preferred pronouns */
  pronouns?: string;
  /** Employee location */
  location?: string;
  /** Manager information */
  manager?: {
    name: string;
    title: string;
    employeeId: number;
  };
  /** Length of service information */
  lengthOfService?: {
    years: number;
    months: number;
    startDate: string;
  };
  /** Last updated timestamp */
  lastUpdated: string;
}

/**
 * Form data structure for profile editing
 */
export interface ProfileEditFormData {
  /** Profile photo URL or base64 data */
  profilePhoto?: string;
  /** Employee biography/description */
  biography?: string;
  /** Preferred pronouns */
  pronouns?: string;
}

/**
 * Pronoun option for dropdown
 */
export interface PronounOption {
  /** Option value */
  value: string;
  /** Display label */
  label: string;
  /** Whether this option is commonly used */
  isCommon?: boolean;
}

/**
 * Profile image upload result
 */
export interface ProfileImageUploadResult {
  /** Uploaded image data (URL or base64) */
  imageData: string;
  /** Original filename */
  filename: string;
  /** File size in bytes */
  fileSize: number;
  /** Upload timestamp */
  uploadedAt: string;
}

/**
 * API request parameters for profile operations
 */
export interface ProfileApiParams {
  /** Employee ID for profile operations */
  employeeId: number;
}

/**
 * API request parameters for profile updates
 */
export interface ProfileUpdateParams extends ProfileApiParams {
  /** Updated profile data */
  data: ProfileEditFormData;
}

/**
 * API response wrapper
 */
export interface ApiResponse<T> {
  /** Response data */
  data: T;
  /** Success status */
  success: boolean;
  /** Error message if any */
  message?: string;
  /** Response timestamp */
  timestamp: string;
}

/**
 * Profile API response types
 */
export type ProfileResponse = ApiResponse<ProfileData>;
export type PronounsResponse = ApiResponse<PronounOption[]>;
export type ProfileUpdateResponse = ApiResponse<ProfileData>;
export type ProfileImageResponse = ApiResponse<ProfileImageUploadResult>;

/**
 * Error types for profile operations
 */
export interface ProfileError {
  /** Error code */
  code: string;
  /** Error message */
  message: string;
  /** Field-specific errors */
  fieldErrors?: Record<string, string>;
}

/**
 * Profile store state interface
 */
export interface ProfileStoreState {
  /** Current profile data */
  profileData: ProfileData | null;
  /** Whether currently editing */
  isEditing: boolean;
  /** Current form data during editing */
  editFormData: ProfileEditFormData | null;
  /** Loading states */
  isLoading: boolean;
  isSubmitting: boolean;
  isUploadingPhoto: boolean;
  /** Error states */
  error: string | null;
  uploadError: string | null;
  /** Photo management */
  photoData: string | null;
  originalPhotoData: string | null;
  /** Dropdown options */
  pronounOptions: PronounOption[];
  /** Computed properties */
  isFormValid: boolean;
  hasChanges: boolean;
  isProcessing: boolean;
}

/**
 * Profile store actions interface
 */
export interface ProfileStoreActions {
  /** Initialize store with employee data */
  initialize: (employeeId: number) => Promise<void>;
  /** Start editing mode */
  startEditing: () => void;
  /** Cancel editing and revert changes */
  cancelEditing: () => void;
  /** Update form field value */
  updateFormField: <K extends keyof ProfileEditFormData>(
    field: K, 
    value: ProfileEditFormData[K]
  ) => void;
  /** Upload profile photo */
  uploadPhoto: (file: File) => Promise<string>;
  /** Remove profile photo */
  removePhoto: () => Promise<void>;
  /** Submit profile changes */
  submitProfile: (data: ProfileEditFormData) => Promise<void>;
  /** Reset store to initial state */
  reset: () => void;
}

/**
 * Hook return type for profile management
 */
export interface UseProfileReturn {
  /** Current store state */
  state: ProfileStoreState;
  /** Available actions */
  actions: ProfileStoreActions;
}

/**
 * Profile header component props
 */
export interface ProfileHeaderProps {
  /** Employee ID to display */
  employeeId?: number;
  /** Test ID for testing */
  testId?: string;
  /** Custom CSS class */
  className?: string;
  /** Whether to show edit functionality */
  showEdit?: boolean;
}

/**
 * Profile edit form component props
 */
export interface ProfileEditFormProps {
  /** Initial form data */
  initialData?: ProfileEditFormData;
  /** Submit handler */
  onSubmit?: (data: ProfileEditFormData) => Promise<void>;
  /** Cancel handler */
  onCancel?: () => void;
  /** Test ID for testing */
  testId?: string;
  /** Whether form is in loading state */
  isLoading?: boolean;
}

/**
 * Profile media object component props
 */
export interface ProfileMediaObjectProps {
  /** Profile photo URL or base64 data */
  photo?: string;
  /** Employee full name */
  fullName: string;
  /** Employee job title */
  jobTitle: string;
  /** Preferred pronouns */
  pronouns?: string;
  /** Test ID for testing */
  testId?: string;
  /** Custom CSS class */
  className?: string;
}
