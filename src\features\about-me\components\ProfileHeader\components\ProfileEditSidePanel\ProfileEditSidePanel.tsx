import React from 'react';
import { usePageShell } from '@ceridianhcm/components';
import { SidePanel } from '@components/SidePanel';
import { useFormWrapper } from '@components/forms/hooks/useFormWrapper';
import { ProfileEditForm } from '../ProfileEditForm';
import { profileEditFormSchema } from '../ProfileEditForm/profile-edit-form-schema';
import { useProfileEditStore } from '../ProfileEditForm/hooks/useProfileEditStore';

interface ProfileEditSidePanelProps {
  open: boolean;
  onClose: () => void;
  testId?: string;
  children?: React.ReactNode;
}

export const ProfileEditSidePanel: React.FC<ProfileEditSidePanelProps> = ({
  open,
  onClose,
  testId = 'edit-profile-sidepanel',
  children,
}) => {
  const { sidePanelSize } = usePageShell();
  const {
    state: { isLoading },
    actions: { submitProfile },
  } = useProfileEditStore();

  const { actionButtons } = useFormWrapper({
    id: 'profile-edit-form',
    schema: profileEditFormSchema,
    defaultValues: {},
    onSubmit: submitProfile,
    onCancel: onClose,
    cancelLabel: 'Cancel',
    isLoading,
  });

  return (
    <SidePanel
      id="edit-profile-panel"
      open={open}
      onClose={onClose}
      title="Edit profile"
      size={sidePanelSize}
      testId={testId}
      actionButtons={actionButtons}
    >
      <ProfileEditForm testId={`${testId}-form`} />
      {children}
    </SidePanel>
  );
};

ProfileEditSidePanel.displayName = 'ProfileEditSidePanel';
