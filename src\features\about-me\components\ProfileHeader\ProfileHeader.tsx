import React from 'react';
import { Container } from '@components/Container';
import { Divider } from '@components/Divider';
import { usePageShell, useSidePanel } from '@components';
import { ProfileMediaObject } from './components/ProfileMediaObject/ProfileMediaObject';
import { ProfileBiography } from './components/ProfileBiography/ProfileBiography';
import { ProfileLocation } from './components/ProfileLocation/ProfileLocation';
import { ProfileManager } from './components/ProfileManager/ProfileManager';
import { ProfileLengthOfService } from './components/ProfileLengthOfService/ProfileLengthOfService';
import { ProfileEditSidePanel } from './components/ProfileEditSidePanel';
import { ProfileEditFormData } from './components/ProfileEditForm/profile-edit-form-schema';
import { mockBio } from './profile-mock-data';

import './profile-header.scss';

interface ProfileHeaderProps {
  testId?: string;
}

/**
 * ProfileHeader displays employee profile information and handles editing functionality.
 * Integrates profile display with edit capabilities in a side panel.
 */
export const ProfileHeader: React.FC<ProfileHeaderProps> = ({ testId = 'profile-header' }) => {
  const profileData: ProfileEditFormData = {
    profilePhoto: mockBio.avatar,
    pronouns: mockBio.pronouns,
    biography: mockBio.biography,
  };

  const { breakpoint } = usePageShell();
  const bioBreakpoint = breakpoint === 'xl' || breakpoint === '2xl' || breakpoint === '3xl';
  const { openPanel, closePanel, isOpen: isEditOpen } = useSidePanel();

  const handleEditClick = () => {
    openPanel();
  };

  const handlePanelClose = () => {
    closePanel();
  };

  const actionProps = {
    actions: [
      {
        name: 'edit' as const,
        'aria-label': 'Edit Profile',
        onClick: handleEditClick,
      },
    ],
    spacing: 8,
  };

  return (
    <>
      <Container
        id="profile-header"
        className="profile-header"
        ariaLabel="Employee Profile"
        dataTestId={testId}
        actionProps={actionProps}
      >
        <div className="profile-header-section">
          <Container.Section divider isHorizontal={bioBreakpoint}>
            <ProfileMediaObject
              photo={profileData.profilePhoto}
              pronouns={profileData.pronouns}
              testId={`${testId}-media`}
            />
            <Divider variant="lowEmp" vertical />

            <ProfileBiography biography={profileData.biography} />
          </Container.Section>
        </div>

        <div className="details-section">
          <Container.Section
            role="region"
            aria-label="Employee Details"
            isHorizontal={breakpoint !== 'md' && breakpoint !== 'sm'}
            horizontalAlign="center"
          >
            <ProfileManager />
            <ProfileLocation />
            <ProfileLengthOfService />
          </Container.Section>
        </div>
      </Container>

      <ProfileEditSidePanel open={isEditOpen} onClose={handlePanelClose} testId={`${testId}-edit-panel`} />
    </>
  );
};

export default ProfileHeader;
