import React from 'react';
import { Container } from '@components/Container';
import { Divider } from '@components/Divider';
import { usePageShell, useSidePanel } from '@components';
import { ProfileMediaObject } from './components/ProfileMediaObject/ProfileMediaObject';
import { ProfileBiography } from './components/ProfileBiography/ProfileBiography';
import { ProfileLocation } from './components/ProfileLocation/ProfileLocation';
import { ProfileManager } from './components/ProfileManager/ProfileManager';
import { ProfileLengthOfService } from './components/ProfileLengthOfService/ProfileLengthOfService';
import { ProfileEditSidePanel } from './components/ProfileEditSidePanel';
import { useProfile } from '../../hooks/useProfile';
import type { ProfileHeaderProps } from '../../types/profile.types';

import './profile-header.scss';

/**
 * ProfileHeader displays employee profile information and handles editing functionality.
 *
 * Refactored to use the new ProfileStore with Platform State for better state management,
 * following established patterns in the HR Profile UI application.
 *
 * Features:
 * - Responsive layout with breakpoint-aware design
 * - Integrated edit functionality with SidePanel
 * - Error handling and loading states
 * - Optimistic updates for better UX
 *
 * @param props - Component props
 * @returns JSX.Element - The rendered ProfileHeader component
 */
export const ProfileHeader: React.FC<ProfileHeaderProps> = ({
  employeeId = 12345, // Default for demo purposes
  testId = 'profile-header',
  className,
  showEdit = true,
}) => {
  const { state, actions } = useProfile(employeeId);
  const { breakpoint } = usePageShell();
  const bioBreakpoint = breakpoint === 'xl' || breakpoint === '2xl' || breakpoint === '3xl';
  const { openPanel, closePanel, isOpen: isEditOpen } = useSidePanel();

  const handleEditClick = () => {
    openPanel();
  };

  const handlePanelClose = () => {
    closePanel();
  };

  const actionProps = {
    actions: [
      {
        name: 'edit' as const,
        'aria-label': 'Edit Profile',
        onClick: handleEditClick,
      },
    ],
    spacing: 8,
  };

  return (
    <ProfileEditProvider
      initialData={{
        profilePhoto: mockBio.avatar,
        biography: mockBio.biography,
        pronouns: mockBio.pronouns,
      }}
    >
      <Container
        id="profile-header"
        className="profile-header"
        ariaLabel="Employee Profile"
        dataTestId={testId}
        actionProps={actionProps}
      >
        <div className="profile-header-section">
          <Container.Section divider isHorizontal={bioBreakpoint}>
            <ProfileMediaObject
              photo={profileData.profilePhoto}
              pronouns={profileData.pronouns}
              testId={`${testId}-media`}
            />
            <Divider variant="lowEmp" vertical />

            <ProfileBiography biography={profileData.biography} />
          </Container.Section>
        </div>

        <div className="details-section">
          <Container.Section
            role="region"
            aria-label="Employee Details"
            isHorizontal={breakpoint !== 'md' && breakpoint !== 'sm'}
            horizontalAlign="center"
          >
            <ProfileManager />
            <ProfileLocation />
            <ProfileLengthOfService />
          </Container.Section>
        </div>
      </Container>

      <ProfileEditSidePanel open={isEditOpen} onClose={handlePanelClose} testId={`${testId}-edit-panel`} />
    </ProfileEditProvider>
  );
};

export default ProfileHeader;
