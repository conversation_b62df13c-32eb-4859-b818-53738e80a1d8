import { useEffect } from 'react';
import { useSource } from '@ceridianhcm/platform-state';
import { ProfileStore } from '../store/ProfileStore';
import type { UseProfileReturn } from '../types/profile.types';

/**
 * Custom hook for profile data management using Platform State
 * 
 * This hook provides a clean interface to the ProfileStore and handles
 * initialization, state management, and actions for profile operations.
 * 
 * Features:
 * - Automatic store initialization
 * - Profile data loading and caching
 * - Form state management
 * - Photo upload and removal
 * - Error handling and loading states
 * 
 * @param employeeId - Employee ID to load profile for
 * @param autoInitialize - Whether to automatically initialize the store (default: true)
 * @returns Profile state and actions
 * 
 * @example
 * ```tsx
 * const { state, actions } = useProfile(12345);
 * 
 * // Access profile data
 * const { profileData, isLoading, error } = state;
 * 
 * // Start editing
 * const handleEdit = () => {
 *   actions.startEditing();
 * };
 * 
 * // Submit changes
 * const handleSubmit = async (data) => {
 *   await actions.submitProfile(data);
 * };
 * ```
 */
export const useProfile = (
  employeeId?: number,
  autoInitialize = true
): UseProfileReturn => {
  const store = useSource(ProfileStore);

  // Initialize store when employeeId changes
  useEffect(() => {
    if (autoInitialize && employeeId && employeeId !== store.profileData?.employeeId) {
      store.initialize(employeeId);
    }
  }, [employeeId, autoInitialize, store]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Only reset if we're the last consumer
      // In a real app, you might want more sophisticated cleanup logic
      if (autoInitialize) {
        store.reset();
      }
    };
  }, [store, autoInitialize]);

  return {
    state: {
      profileData: store.profileData,
      isEditing: store.isEditing,
      editFormData: store.editFormData,
      isLoading: store.isLoading,
      isSubmitting: store.isSubmitting,
      isUploadingPhoto: store.isUploadingPhoto,
      error: store.error,
      uploadError: store.uploadError,
      photoData: store.photoData,
      originalPhotoData: store.originalPhotoData,
      pronounOptions: store.pronounOptions,
      isFormValid: store.isFormValid,
      hasChanges: store.hasChanges,
      isProcessing: store.isProcessing,
    },
    actions: {
      initialize: store.initialize.bind(store),
      startEditing: store.startEditing.bind(store),
      cancelEditing: store.cancelEditing.bind(store),
      updateFormField: store.updateFormField.bind(store),
      uploadPhoto: store.uploadPhoto.bind(store),
      removePhoto: store.removePhoto.bind(store),
      submitProfile: store.submitProfile.bind(store),
      reset: store.reset.bind(store),
    },
  };
};

/**
 * Hook for profile editing with form integration
 * 
 * This is a specialized version of useProfile that's optimized for form usage.
 * It automatically starts editing mode and provides form-specific utilities.
 * 
 * @param employeeId - Employee ID to edit
 * @returns Profile editing state and actions
 * 
 * @example
 * ```tsx
 * const { state, actions, formHelpers } = useProfileEdit(12345);
 * 
 * // Form integration
 * const methods = useForm({
 *   defaultValues: state.editFormData,
 * });
 * 
 * // Sync form with store
 * useEffect(() => {
 *   if (state.editFormData) {
 *     methods.reset(state.editFormData);
 *   }
 * }, [state.editFormData, methods]);
 * ```
 */
export const useProfileEdit = (employeeId?: number) => {
  const profile = useProfile(employeeId);

  // Automatically start editing when profile is loaded
  useEffect(() => {
    if (profile.state.profileData && !profile.state.isEditing) {
      profile.actions.startEditing();
    }
  }, [profile.state.profileData, profile.state.isEditing, profile.actions]);

  return {
    ...profile,
    formHelpers: {
      /**
       * Get default values for form initialization
       */
      getDefaultValues: () => profile.state.editFormData || {
        profilePhoto: '',
        biography: '',
        pronouns: '',
      },
      
      /**
       * Check if form has validation errors
       */
      hasValidationErrors: () => !profile.state.isFormValid,
      
      /**
       * Get form submission handler
       */
      getSubmitHandler: () => profile.actions.submitProfile,
      
      /**
       * Get form cancel handler
       */
      getCancelHandler: () => profile.actions.cancelEditing,
    },
  };
};

/**
 * Hook for profile photo management
 * 
 * Specialized hook for handling profile photo operations with enhanced
 * file validation and progress tracking.
 * 
 * @param employeeId - Employee ID for photo operations
 * @returns Photo management state and actions
 * 
 * @example
 * ```tsx
 * const { photoState, photoActions } = useProfilePhoto(12345);
 * 
 * const handleFileSelect = async (file: File) => {
 *   if (photoActions.validateFile(file)) {
 *     await photoActions.uploadPhoto(file);
 *   }
 * };
 * ```
 */
export const useProfilePhoto = (employeeId?: number) => {
  const { state, actions } = useProfile(employeeId, false);

  return {
    photoState: {
      currentPhoto: state.photoData,
      originalPhoto: state.originalPhotoData,
      isUploading: state.isUploadingPhoto,
      uploadError: state.uploadError,
      hasPhoto: !!state.photoData,
      hasChanges: state.photoData !== state.originalPhotoData,
    },
    photoActions: {
      uploadPhoto: actions.uploadPhoto,
      removePhoto: actions.removePhoto,
      
      /**
       * Validate file before upload
       */
      validateFile: (file: File): boolean => {
        const maxSize = 5 * 1024 * 1024; // 5MB
        const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        
        if (file.size > maxSize) {
          return false;
        }
        
        if (!allowedTypes.includes(file.type)) {
          return false;
        }
        
        return true;
      },
      
      /**
       * Get file validation error message
       */
      getValidationError: (file: File): string | null => {
        const maxSize = 5 * 1024 * 1024; // 5MB
        const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        
        if (file.size > maxSize) {
          return 'File size must be less than 5MB';
        }
        
        if (!allowedTypes.includes(file.type)) {
          return 'File must be a valid image (JPEG, PNG, GIF, or WebP)';
        }
        
        return null;
      },
    },
  };
};

/**
 * Hook for profile data without editing capabilities
 * 
 * Lightweight hook for read-only profile display.
 * 
 * @param employeeId - Employee ID to display
 * @returns Read-only profile state
 * 
 * @example
 * ```tsx
 * const profile = useProfileDisplay(12345);
 * 
 * if (profile.isLoading) return <Spinner />;
 * if (profile.error) return <ErrorMessage />;
 * 
 * return <ProfileCard data={profile.data} />;
 * ```
 */
export const useProfileDisplay = (employeeId?: number) => {
  const { state } = useProfile(employeeId);

  return {
    data: state.profileData,
    isLoading: state.isLoading,
    error: state.error,
    isReady: !!state.profileData && !state.isLoading,
  };
};
