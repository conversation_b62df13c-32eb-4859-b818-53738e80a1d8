/**
 * Profile API service following the co-located feature organization pattern
 * Handles all profile-related API operations
 */

import type {
  ProfileData,
  ProfileEditFormData,
  PronounOption,
  ProfileResponse,
  PronounsResponse,
  ProfileUpdateResponse,
  ProfileApiParams,
  ProfileUpdateParams,
} from '../types/profile.types';

// Mock delay for realistic API simulation
const MOCK_DELAY = 800;

/**
 * Simulates API delay for realistic testing
 */
const delay = (ms: number): Promise<void> => 
  new Promise(resolve => setTimeout(resolve, ms));

/**
 * Profile API service class
 * Provides methods for profile data operations
 */
class ProfileApiService {
  /**
   * Get employee profile data
   * 
   * @param employeeId - Employee unique identifier
   * @returns Promise resolving to profile data
   */
  async getProfile(employeeId: number): Promise<ProfileResponse> {
    await delay(MOCK_DELAY);
    
    try {
      // In a real implementation, this would make an HTTP request
      // For now, we'll return mock data based on the employee ID
      const mockProfile: ProfileData = {
        employeeId,
        fullName: '<PERSON>',
        jobTitle: 'Senior Software Engineer',
        profilePhoto: '/api/images/profile/default-avatar.jpg',
        biography: 'Passionate software engineer with 8+ years of experience in full-stack development. Specializes in React, TypeScript, and cloud architecture. Enjoys mentoring junior developers and contributing to open-source projects.',
        pronouns: 'he/him',
        location: 'Toronto, ON, Canada',
        manager: {
          name: 'Jane Smith',
          title: 'Engineering Manager',
          employeeId: 12345,
        },
        lengthOfService: {
          years: 3,
          months: 7,
          startDate: '2021-06-15',
        },
        lastUpdated: new Date().toISOString(),
      };

      return {
        data: mockProfile,
        success: true,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      throw new Error(`Failed to fetch profile for employee ${employeeId}: ${error}`);
    }
  }

  /**
   * Get available pronoun options
   * 
   * @returns Promise resolving to pronoun options
   */
  async getPronouns(): Promise<PronounsResponse> {
    await delay(MOCK_DELAY / 2);
    
    try {
      const pronounOptions: PronounOption[] = [
        { value: '', label: 'Select pronouns', isCommon: false },
        { value: 'he/him', label: 'he/him', isCommon: true },
        { value: 'she/her', label: 'she/her', isCommon: true },
        { value: 'they/them', label: 'they/them', isCommon: true },
        { value: 'xe/xem', label: 'xe/xem', isCommon: false },
        { value: 'ze/zir', label: 'ze/zir', isCommon: false },
        { value: 'prefer-not-to-say', label: 'Prefer not to say', isCommon: false },
      ];

      return {
        data: pronounOptions,
        success: true,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      throw new Error(`Failed to fetch pronoun options: ${error}`);
    }
  }

  /**
   * Update employee profile
   * 
   * @param employeeId - Employee unique identifier
   * @param data - Updated profile data
   * @returns Promise resolving to updated profile data
   */
  async updateProfile(employeeId: number, data: ProfileEditFormData): Promise<ProfileUpdateResponse> {
    await delay(MOCK_DELAY);
    
    try {
      // Validate required fields
      if (!employeeId) {
        throw new Error('Employee ID is required');
      }

      // In a real implementation, this would make an HTTP PUT/PATCH request
      // For now, we'll simulate the update and return the updated data
      const currentProfile = await this.getProfile(employeeId);
      
      const updatedProfile: ProfileData = {
        ...currentProfile.data,
        ...data,
        lastUpdated: new Date().toISOString(),
      };

      return {
        data: updatedProfile,
        success: true,
        message: 'Profile updated successfully',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      throw new Error(`Failed to update profile for employee ${employeeId}: ${error}`);
    }
  }

  /**
   * Validate profile data before submission
   * 
   * @param data - Profile data to validate
   * @returns Validation result
   */
  validateProfileData(data: ProfileEditFormData): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Biography validation
    if (data.biography && data.biography.length > 1000) {
      errors.push('Biography must be less than 1000 characters');
    }

    // Pronouns validation
    if (data.pronouns && !this.isValidPronoun(data.pronouns)) {
      errors.push('Invalid pronoun selection');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Check if a pronoun value is valid
   * 
   * @param pronoun - Pronoun value to validate
   * @returns Whether the pronoun is valid
   */
  private isValidPronoun(pronoun: string): boolean {
    const validPronouns = [
      'he/him',
      'she/her',
      'they/them',
      'xe/xem',
      'ze/zir',
      'prefer-not-to-say',
    ];
    
    return validPronouns.includes(pronoun);
  }

  /**
   * Get profile by multiple criteria (for future extensibility)
   * 
   * @param params - Search parameters
   * @returns Promise resolving to profile data
   */
  async searchProfiles(params: {
    employeeIds?: number[];
    department?: string;
    location?: string;
  }): Promise<ProfileResponse[]> {
    await delay(MOCK_DELAY);
    
    try {
      // In a real implementation, this would handle complex search queries
      const results: ProfileResponse[] = [];
      
      if (params.employeeIds) {
        for (const employeeId of params.employeeIds) {
          const profile = await this.getProfile(employeeId);
          results.push(profile);
        }
      }
      
      return results;
    } catch (error) {
      throw new Error(`Failed to search profiles: ${error}`);
    }
  }
}

/**
 * Singleton instance of the profile API service
 */
export const profileApi = new ProfileApiService();

/**
 * Export individual methods for easier testing and mocking
 */
export const {
  getProfile,
  getPronouns,
  updateProfile,
  validateProfileData,
  searchProfiles,
} = profileApi;
